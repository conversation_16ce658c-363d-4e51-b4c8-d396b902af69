This directory holds build tools, i.e. tools we use to build the current
code tree. Packaging tools (which take released tarballs or github code
repos) should go into /packaging.

Note: 'deprecated-msvc' contains deprecated prepared Visual Studio Solution 
files for various Visual Studio versions. These are no longer maintained, 
and may or may not work. Please use cmake instead to build with Visual
Studio. Rationale: The solution and project files are hard to maintain,
since there are different variants for each Visual Studio version.
The tests have never been included there for this reason, so they were
never usable for actual development of libzmq. If you encounter that 
something that worked before does not work with CMake, please open as 
issue at https://github.com/zeromq/libzmq/issues.

