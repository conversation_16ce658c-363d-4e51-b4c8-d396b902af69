LIBZMQ_DIST = vs2008/libzmq.sln \
              vs2008/libzmq/libzmq.vcproj \
              vs2010/libzmq.sln \
              vs2010/libzmq/libzmq.vcxproj \
              vs2010/libzmq/libzmq.vcxproj.filters \
              vs2012/libzmq.sln \
              vs2012/libzmq/libzmq.vcxproj \
              vs2012/libzmq/libzmq.vcxproj.filters \
              vs2013/libzmq.sln \
              vs2013/libzmq/libzmq.vcxproj \
              vs2013/libzmq/libzmq.vcxproj.filters \
              vs2015/libzmq.sln \
              vs2015/libzmq/libzmq.vcxproj \
              vs2015/libzmq/libzmq.vcxproj.filters \
              vs2015/libzmq/libzmq.props \
              vs2015/libzmq/libzmq.xml \
              vs2015/libzmq.import.props \
              vs2015/libzmq.import.xml \
              errno.cpp \
              errno.hpp \
              platform.hpp \
              resource.h \
              resource.rc

PERF_DIST = vs2008/local_lat/local_lat.vcproj \
            vs2008/local_thr/local_thr.vcproj \
            vs2008/remote_lat/remote_lat.vcproj \
            vs2008/remote_thr/remote_thr.vcproj \
            vs2008/inproc_lat/inproc_lat.vcproj \
            vs2008/inproc_thr/inproc_thr.vcproj \
            vs2010/local_lat/local_lat.vcxproj \
            vs2010/local_thr/local_thr.vcxproj \
            vs2010/remote_lat/remote_lat.vcxproj \
            vs2010/remote_thr/remote_thr.vcxproj \
            vs2010/inproc_lat/inproc_lat.vcxproj \
            vs2010/inproc_thr/inproc_thr.vcxproj \
            vs2012/local_lat/local_lat.vcxproj \
            vs2012/local_thr/local_thr.vcxproj \
            vs2012/remote_lat/remote_lat.vcxproj \
            vs2012/remote_thr/remote_thr.vcxproj \
            vs2012/inproc_lat/inproc_lat.vcxproj \
            vs2012/inproc_thr/inproc_thr.vcxproj \
            vs2013/local_lat/local_lat.vcxproj \
            vs2013/local_thr/local_thr.vcxproj \
            vs2013/remote_lat/remote_lat.vcxproj \
            vs2013/remote_thr/remote_thr.vcxproj \
            vs2013/inproc_lat/inproc_lat.vcxproj \
            vs2013/inproc_thr/inproc_thr.vcxproj \
            vs2015/local_lat/local_lat.vcxproj \
            vs2015/local_lat/local_lat.props \
            vs2015/local_thr/local_thr.vcxproj \
            vs2015/local_thr/local_thr.props \
            vs2015/remote_lat/remote_lat.vcxproj \
            vs2015/remote_lat/remote_lat.props \
            vs2015/remote_thr/remote_thr.vcxproj \
            vs2015/remote_thr/remote_thr.props \
            vs2015/inproc_lat/inproc_lat.vcxproj \
            vs2015/inproc_lat/inproc_lat.props \
            vs2015/inproc_thr/inproc_thr.vcxproj \
            vs2015/inproc_thr/inproc_thr.props

PROPERTIES_DIST = properties/Common.props \
                  properties/Debug.props \
                  properties/DebugDEXE.props \
                  properties/DebugDLL.props \
                  properties/DebugLEXE.props \
                  properties/DebugLIB.props \
                  properties/DebugLTCG.props \
                  properties/DebugSEXE.props \
                  properties/DLL.props \
                  properties/EXE.props \
                  properties/LIB.props \
                  properties/Link.props \
                  properties/LTCG.props \
                  properties/Messages.props \
                  properties/Output.props \
                  properties/Release.props \
                  properties/ReleaseDEXE.props \
                  properties/ReleaseDLL.props \
                  properties/ReleaseLEXE.props \
                  properties/ReleaseLIB.props \
                  properties/ReleaseLTCG.props \
                  properties/ReleaseSEXE.props \
                  properties/Win32.props \
                  properties/x64.props

PRECOMPILED_DIST = ../../src/precompiled.hpp \
                   ../../src/precompiled.cpp

BUILD_DIST = build/build.bat \
             build/buildall.bat \
             build/buildbase.bat

EXTRA_DIST = $(LIBZMQ_DIST) $(PERF_DIST) $(PROPERTIES_DIST) $(PRECOMPILED_DIST) $(BUILD_DIST)