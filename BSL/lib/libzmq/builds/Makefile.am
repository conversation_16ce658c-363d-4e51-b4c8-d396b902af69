#  Specify all build files that have to go into source packages.
#  msvc directory does its own stuff.

EXTRA_DIST = \
 	cygwin/Makefile.cygwin \
	zos/makelibzmq \
	zos/cxxall \
	zos/README.md \
	zos/makeclean \
	zos/platform.hpp \
	zos/zc++ \
	zos/test_fork.cpp \
	zos/maketests \
	zos/runtests \
	cygwin/Makefile.cygwin \
	mingw32/Makefile.mingw32 \
	mingw32/platform.hpp \
	cmake/ci_build.sh \
	cmake/Modules \
	cmake/NSIS.template32.in \
	cmake/NSIS.template64.in \
	cmake/ZeroMQConfig.cmake.in \
	cmake/clang-format-check.sh.in \
	cmake/platform.hpp.in \
	valgrind/ci_build.sh \
	valgrind/valgrind.supp \
	valgrind/vg \
	nuget/readme.nuget \
	nuget/libzmq.autopkg \
	android/Dockerfile \
	android/README.md \
	android/android_build_helper.sh \
	android/ci_build.sh \
	android/build.sh
