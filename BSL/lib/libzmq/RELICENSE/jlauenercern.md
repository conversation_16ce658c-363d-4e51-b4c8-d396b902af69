# Permission to Relicense under MPLv2 or any other share-alike OSI approved license chosen by the current ZeroMQ BDFL

This is a statement by <PERSON>
that grants permission to relicense its copyrights in the libzmq C++
library (ZeroMQ) under the Mozilla Public License v2 (MPLv2) or any other 
share-alike Open Source Initiative approved license chosen by the current 
ZeroMQ BDFL (Benevolent Dictator for Life).

A portion of the commits made by the Github handle "jlauenercern", with
commit author "<PERSON> <Joel.<PERSON>@cern.ch>", are copyright of <PERSON> .
This document hereby grants the libzmq project team to relicense libzmq, 
including all past, present and future contributions of the author listed above.

<PERSON> 
2019/08/12