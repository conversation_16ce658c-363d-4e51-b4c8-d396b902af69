# Permission to Relicense under MPLv2

This is a statement by Spotify AB that grants permission to relicense its
copyrights in the libzmq C++ library (ZeroMQ) under the Mozilla Public License
v2 (MPLv2).

A portion of the commits made by the Github handle "gimaker", with commit author
"<PERSON><PERSON>", the commits made by the Github handle "danie<PERSON><PERSON><PERSON>", with
commit author "<PERSON>", and the commits made by the Github handle
"caipre", with commit author "<PERSON>", are copyright of Spotify AB. This
document hereby grants the libzmq project team to relicense libzmq, including
all past, present and future contributions of the authors listed above.

<PERSON> <<EMAIL>>
2019/08/21
