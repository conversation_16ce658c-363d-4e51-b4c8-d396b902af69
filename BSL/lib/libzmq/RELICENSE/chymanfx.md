# Permission to Relicense under MPLv2 or any other OSI approved license chosen by the current ZeroMQ BDFL

I happily grant permission to
relicense its copyrights in the libzmq C++ library (ZeroMQ) under the
Mozilla Public License v2 (MPLv2) or any other Open Source Initiative
approved license chosen by the current ZeroMQ BDFL (Benevolent
Dictator for Life).

The portion of the commits made by the Github handle "chymanfx", are
not copyrighted in any way.
Chyman
2019/08/12
