# Permission to Relicense under MPLv2

This is a statement by Floris Bruynooghe
that grants permission to relicense its copyrights in the libzmq C++
library (ZeroMQ) under the Mozilla Public License v2 (MPLv2).

A portion of the commits made by the Github handle "flub", with
commit author "<PERSON><PERSON><PERSON> <<EMAIL>>", are copyright of
Floris Bruynooghe .
This document hereby grants the libzmq project team to relicense libzmq,
including all past, present and future contributions of the author listed above.

Floris Bruynooghe
2017/05/03
