# Permission to Relicense under MPLV2

This document collects grants from firms that hold copyrights in ZeroMQ. Please add new firms at the start. Each patch must be made with a GitHub handle that guarantees identity of the signatory.

## iMatix Corporation

This is a statement by iMatix Corporation sprl (iMatix) that grants permission to relicense its copyrights in the libzmq C++ library (ZeroMQ) under the Mozilla Public License v2 (MPLv2).

1. The original ZeroMQ codebase was developed from 2007-2009 by FastMQ Inc. (FastMQ) in Slovakia, which held all copyrights in code written by its staff.

2. On 2009-11-01, iMatix exercised an option to acquire FastMQ and its assets,
including ZeroMQ. On or before 2010-01-05 the FastMQ copyrights in ZeroMQ were
transferred to iMatix ([commit](https://github.com/zeromq/libzmq/commit/4f6baf4dde627656b63cc4e2acdb78a8577ba640)). The FastMQ legal entity was subsequently liquidated.

3. Between 2009 and 2016, iMatix has continued to add contributions to ZeroMQ under the GitHub handle 'hintjens'.

4. This grant therefore covers what remains of the original FastMQ codebase plus all later contributions.

5. iMatix hereby grants an irrevocable, global, and fully paid-up license on all its copyrights that exist in ZeroMQ, under the MPLv2.

-<PERSON> <PERSON>nt<PERSON><PERSON>
CEO, iMatix Corporation sprl
23 April 2016
