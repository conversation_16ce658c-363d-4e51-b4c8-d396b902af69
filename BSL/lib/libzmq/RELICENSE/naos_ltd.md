## Naos Ltd (a New Zealand company)

This is a statement by Naos Ltd (Naos) that grants permission to
relicense its copyrights in the libzmq C++ library (ZeroMQ) under
the Mozilla Public License v2 (MPLv2).

The port of libzmq to run on the z/OS Mainframe ([GitHub Pull
request #1136](https://github.com/zeromq/libzmq/pull/1136), [GitHub Pull
request #1138](https://github.com/zeromq/libzmq/pull/1138), and
[GitHub Pull request #1139](https://github.com/zeromq/libzmq/pull/1139))
was performed as work for hire under contract to iMatix Corporation
sprl, itself under contract to a client.  Thus copyright in that
portability work does not belong to Naos Ltd, and Naos Ltd hereby
releases any claim to the copyright in the z/OS Mainframe portability
work identified by the above three GitHub pull requests.

<PERSON><PERSON>  
Managing Director, Naos Ltd  
2016-04-25
