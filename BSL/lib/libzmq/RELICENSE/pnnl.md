# Permission to Relicense under MPLv2 or any other OSI approved license chosen by the current ZeroMQ BDFL

This is a statement by Pacific Northwest National Laboratory (Battelle Memorial Institute, Pacific Northwest Division)
that grants permission to relicense its copyrights in the libzmq C++
library (ZeroMQ) under the Mozilla Public License v2 (MPLv2) or any other
Open Source Initiative approved license (at least as permissive as the MPLv2) chosen by the current ZeroMQ
BDFL (Benevolent Dictator for Life).

A portion of the commits made by the Github handle "hashstat", with
commit author "<PERSON> <<EMAIL>>", are copyright of Pacific Northwest National Laboratory (Battelle Memorial Institute, Pacific Northwest Division).
This document hereby grants the libzmq project team to relicense libzmq,
including all past, present and future contributions of the author listed above.

<PERSON><PERSON><PERSON> on behalf of <PERSON>
2019/02/18
