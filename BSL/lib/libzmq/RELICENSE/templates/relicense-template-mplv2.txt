# Permission to Relicense under MPLv2

This is a statement by {{ name of company / name of individual }}
that grants permission to relicense its copyrights in the libzmq C++
library (ZeroMQ) under the Mozilla Public License v2 (MPLv2).

A portion of the commits made by the Github handle "{{github username}}", with
commit author "{{github commit author}}", are copyright of {{ name }} .
This document hereby grants the libzmq project team to relicense libzmq, 
including all past, present and future contributions of the author listed above.

{{ Full Name }}  
{{ creation date of document (format: yyyy/mm/dd) }}