# Permission to Relicense under MPLv2

This is a statement by trya.
that grants permission to relicense its copyrights in the libzmq C++
library (ZeroMQ) under the Mozilla Public License v2 (MPLv2).

A portion of the commits made by the Github handle "trya", with
commit author "trya", are copyright of trya.
This document hereby grants the libzmq project team to relicense libzmq, 
including all past, present and future contributions of the author listed above.

trya
2019/09/23
