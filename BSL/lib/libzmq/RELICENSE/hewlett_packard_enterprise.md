# Permission to Relicense under MPLv2

This is a statement by Hewlett Packard Enterprise
that grants permission to relicense its copyrights in the libzmq C++
library (ZeroMQ) under the Mozilla Public License v2 (MPLv2).

A portion of the commits made by the Github handle "brc859844", with
commit author "<PERSON> <<PERSON>@hp.com>", are copyright of Hewlett Packard Enterprise.
This document hereby grants the libzmq project team to relicense libzmq,
including all past, present and future contributions of the author listed above.

Hewlett Packard Enterprise
2019/03/12
