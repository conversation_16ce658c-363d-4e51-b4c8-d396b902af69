# Permission to Relicense under MPLv2

This is a statement by Google, Inc.
that grants permission to relicense its copyrights in the libzmq C++
library (ZeroMQ) under the Mozilla Public License v2 (MPLv2).

A portion of the commits made by the Github handle "tkoeppe", with
commit author "<PERSON>", are copyright of Google, Inc.
This document hereby grants the libzmq project team to relicense libzmq, 
including all past, present and future contributions of the author listed above.

<PERSON>
2017/09/13
