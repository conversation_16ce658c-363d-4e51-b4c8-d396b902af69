# Permission to Relicense under MPLV2

This directory collects grants from individuals and firms that hold
copyrights in ZeroMQ to permit licensing the ZeroMQ code under
the [Mozilla Public License, version
2](https://www.mozilla.org/en-US/MPL/2.0/).  See [GitHub Pull
Request #1917](https://github.com/zeromq/libzmq/pull/1917),
the [0MQ Licensing Page](http://zeromq.org/area:licensing) and
[original iMatix zeromq-dev license
grant](http://lists.zeromq.org/pipermail/zeromq-dev/2016-April/030288.html)
for some background information.

Please create a separate file in this directory for each individual
or firm holding copyright in ZeroMQ, named after the individual or
firm holding the copyright.

Each patch must be made with a GitHub handle that is clearly
associated with the copyright owner, to guarantee the identity of
the signatory.  Please avoid changing the files created by other
individuals or firms granting a copyright license over their
copyrights (if rewording is required contact them and ask them to
submit an updated version).  This makes it easier to verify that
the license grant was made by an authorized GitHub account.
