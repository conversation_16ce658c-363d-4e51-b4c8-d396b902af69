zmq_ipc(7)
==========


NAME
----
zmq_ipc - 0MQ local inter-process communication transport


SYNOPSIS
--------
The inter-process transport passes messages between local processes using a
system-dependent IPC mechanism.

NOTE: The inter-process transport is currently only implemented on operating
systems that provide UNIX domain sockets.


ADDRESSING
----------
A 0MQ endpoint is a string consisting of a 'transport'`://` followed by an
'address'. The 'transport' specifies the underlying protocol to use. The
'address' specifies the transport-specific address to connect to.

For the inter-process transport, the transport is `ipc`, and the meaning of
the 'address' part is defined below.


Binding a socket
~~~~~~~~~~~~~~~~
When binding a 'socket' to a local address using _zmq_bind()_ with the 'ipc'
transport, the 'endpoint' shall be interpreted as an arbitrary string
identifying the 'pathname' to create. The 'pathname' must be unique within the
operating system namespace used by the 'ipc' implementation, and must fulfill
any restrictions placed by the operating system on the format and length of a
'pathname'.

When the address is wild-card `*`, _zmq_bind()_ shall generate a unique temporary
pathname. The caller should retrieve this pathname using the ZMQ_LAST_ENDPOINT
socket option. See linkzmq:zmq_getsockopt[3] for details.

NOTE: any existing binding to the same endpoint shall be overridden. That is,
if a second process binds to an endpoint already bound by a process, this
will succeed and the first process will lose its binding. In this behaviour,
the 'ipc' transport is not consistent with the 'tcp' or 'inproc' transports.

NOTE: the endpoint pathname must be writable by the process. When the endpoint
starts with '/', e.g., `ipc:///pathname`, this will be an _absolute_ pathname.
If the endpoint specifies a directory that does not exist, the bind shall fail.

NOTE: on Linux only, when the endpoint pathname starts with `@`, the abstract
namespace shall be used.  The abstract namespace is independent of the
filesystem and if a process attempts to bind an endpoint already bound by a
process, it will fail.  See unix(7) for details.

NOTE: IPC pathnames have a maximum size that depends on the operating system.
On Linux, the maximum is 113 characters including the "ipc://" prefix (107
characters for the real path name).

Unbinding wild-card address from a socket
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
When wild-card `*` 'endpoint' was used in _zmq_bind()_, the caller should use
real 'endpoint' obtained from the ZMQ_LAST_ENDPOINT socket option to unbind
this 'endpoint' from a socket using _zmq_unbind()_.

Connecting a socket
~~~~~~~~~~~~~~~~~~~
When connecting a 'socket' to a peer address using _zmq_connect()_ with the
'ipc' transport, the 'endpoint' shall be interpreted as an arbitrary string
identifying the 'pathname' to connect to.  The 'pathname' must have been
previously created within the operating system namespace by assigning it to a
'socket' with _zmq_bind()_.


EXAMPLES
--------
.Assigning a local address to a socket
----
//  Assign the pathname "/tmp/feeds/0"
rc = zmq_bind(socket, "ipc:///tmp/feeds/0");
assert (rc == 0);
----

.Connecting a socket
----
//  Connect to the pathname "/tmp/feeds/0"
rc = zmq_connect(socket, "ipc:///tmp/feeds/0");
assert (rc == 0);
----

SEE ALSO
--------
linkzmq:zmq_bind[3]
linkzmq:zmq_connect[3]
linkzmq:zmq_inproc[7]
linkzmq:zmq_tcp[7]
linkzmq:zmq_pgm[7]
linkzmq:zmq_vmci[7]
linkzmq:zmq_getsockopt[3]
linkzmq:zmq[7]


AUTHORS
-------
This page was written by the 0MQ community. To make a change please
read the 0MQ Contribution Policy at <http://www.zeromq.org/docs:contributing>.
