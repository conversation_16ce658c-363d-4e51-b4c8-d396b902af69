#
# documentation
#
MAN3 = zmq_bind.3 zmq_unbind.3 zmq_connect.3 zmq_disconnect.3 zmq_close.3 \
    zmq_ctx_new.3 zmq_ctx_term.3 zmq_ctx_get.3 zmq_ctx_set.3 zmq_ctx_shutdown.3 \
    zmq_msg_init.3 zmq_msg_init_data.3 zmq_msg_init_size.3 \
    zmq_msg_move.3 zmq_msg_copy.3 zmq_msg_size.3 zmq_msg_data.3 zmq_msg_close.3 \
    zmq_msg_send.3 zmq_msg_recv.3 \
    zmq_msg_routing_id.3 zmq_msg_set_routing_id.3 \
    zmq_send.3 zmq_recv.3 zmq_send_const.3 \
    zmq_msg_get.3 zmq_msg_set.3 zmq_msg_more.3 zmq_msg_gets.3 \
    zmq_getsockopt.3 zmq_setsockopt.3 \
    zmq_socket.3 zmq_socket_monitor.3 zmq_poll.3 \
    zmq_socket_monitor_versioned.3 \
    zmq_errno.3 zmq_strerror.3 zmq_version.3 \
    zmq_sendmsg.3 zmq_recvmsg.3 \
    zmq_proxy.3 zmq_proxy_steerable.3 \
    zmq_z85_encode.3 zmq_z85_decode.3 zmq_curve_keypair.3 zmq_curve_public.3 \
    zmq_has.3 \
    zmq_timers.3 zmq_poller.3 \
    zmq_atomic_counter_new.3 zmq_atomic_counter_set.3 \
    zmq_atomic_counter_inc.3 zmq_atomic_counter_dec.3 \
    zmq_atomic_counter_value.3 zmq_atomic_counter_destroy.3

MAN7 = zmq.7 zmq_tcp.7 zmq_pgm.7 zmq_inproc.7 zmq_ipc.7 \
    zmq_null.7 zmq_plain.7 zmq_curve.7 zmq_tipc.7 zmq_vmci.7 zmq_udp.7 \
    zmq_gssapi.7

MAN_DOC =

MAN_TXT = $(MAN3:%.3=%.txt)
MAN_TXT += $(MAN7:%.7=%.txt)
MAN_HTML =

MAINTAINERCLEANFILES =

EXTRA_DIST = asciidoc.conf $(MAN_TXT)

if INSTALL_MAN
MAN_DOC += $(MAN1) $(MAN3) $(MAN7)
dist_man_MANS = $(MAN_DOC)
MAINTAINERCLEANFILES += $(MAN_DOC)
endif

if BUILD_DOC
MAN_HTML += $(MAN_TXT:%.txt=%.html)
EXTRA_DIST += $(MAN_HTML)
MAINTAINERCLEANFILES += $(MAN_HTML)

SUFFIXES=.html .txt .xml .3 .7

.txt.html:
	asciidoc -d manpage -b xhtml11 -f $(srcdir)/asciidoc.conf \
		-azmq_version=@PACKAGE_VERSION@ -o$@ $<
.txt.xml:
	asciidoc -d manpage -b docbook -f $(srcdir)/asciidoc.conf \
		-azmq_version=@PACKAGE_VERSION@ -o$@ $<
.xml.1:
	xmlto man $<
.xml.3:
	xmlto man $<
.xml.7:
	xmlto man $<
endif

dist-hook : $(MAN_DOC) $(MAN_HTML)
