zmq_msg_copy(3)
===============


NAME
----
zmq_msg_copy - copy content of a message to another message


SYNOPSIS
--------
*int zmq_msg_copy (zmq_msg_t '*dest', zmq_msg_t '*src');*


DESCRIPTION
-----------
The _zmq_msg_copy()_ function shall copy the message object referenced by 'src'
to the message object referenced by 'dest'. The original content of 'dest', if
any, shall be released. You must initialise 'dest' before copying to it.

CAUTION: The implementation may choose not to physically copy the message
content, rather to share the underlying buffer between 'src' and 'dest'. Avoid
modifying message content after a message has been copied with
_zmq_msg_copy()_, doing so can result in undefined behaviour. If what you need
is an actual hard copy, allocate a new message using _zmq_msg_init_size()_ and
copy the message content using _memcpy()_.

CAUTION: Never access 'zmq_msg_t' members directly, instead always use the
_zmq_msg_ family of functions.


RETURN VALUE
------------
The _zmq_msg_copy()_ function shall return zero if successful. Otherwise it
shall return `-1` and set 'errno' to one of the values defined below.


ERRORS
------
*EFAULT*::
Invalid message.


EXAMPLE
-------
.Copying a message
----
zmq_msg_t msg;
zmq_msg_init_size (&msg, 255);
memcpy (zmq_msg_data (&msg, "Hello, World", 12);
zmq_msg_t copy;
zmq_msg_init (&copy);
zmq_msg_copy (&copy, &msg);
...
zmq_msg_close (&copy);
zmq_msg_close (&msg);
----

SEE ALSO
--------
linkzmq:zmq_msg_move[3]
linkzmq:zmq_msg_init[3]
linkzmq:zmq_msg_init_size[3]
linkzmq:zmq_msg_init_data[3]
linkzmq:zmq_msg_close[3]
linkzmq:zmq[7]


AUTHORS
-------
This page was written by the 0MQ community. To make a change please
read the 0MQ Contribution Policy at <http://www.zeromq.org/docs:contributing>.
