zmq_atomic_counter_dec(3)
=========================


NAME
----
zmq_atomic_counter_dec - decrement an atomic counter


SYNOPSIS
--------
*int zmq_atomic_counter_dec (void *counter);*


DESCRIPTION
-----------
The _zmq_atomic_counter_dec_ function decrements an atomic counter in
a threadsafe fashion. This function uses platform specific atomic
operations.


RETURN VALUE
------------
The _zmq_atomic_counter_dec()_ function returns 1 if the counter is
greater than zero after decrementing, or zero if the counter reached
zero.


EXAMPLE
-------
.Test code for atomic counters
----
void *counter = zmq_atomic_counter_new ();
assert (zmq_atomic_counter_value (counter) == 0);
assert (zmq_atomic_counter_inc (counter) == 0);
assert (zmq_atomic_counter_inc (counter) == 1);
assert (zmq_atomic_counter_inc (counter) == 2);
assert (zmq_atomic_counter_value (counter) == 3);
assert (zmq_atomic_counter_dec (counter) == 1);
assert (zmq_atomic_counter_dec (counter) == 1);
assert (zmq_atomic_counter_dec (counter) == 0);
zmq_atomic_counter_set (counter, 2);
assert (zmq_atomic_counter_dec (counter) == 1);
assert (zmq_atomic_counter_dec (counter) == 0);
zmq_atomic_counter_destroy (&counter);
return 0;
----


SEE ALSO
--------
linkzmq:zmq_atomic_counter_new[3]
linkzmq:zmq_atomic_counter_set[3]
linkzmq:zmq_atomic_counter_inc[3]
linkzmq:zmq_atomic_counter_value[3]
linkzmq:zmq_atomic_counter_destroy[3]


AUTHORS
-------
This page was written by the 0MQ community. To make a change please
read the 0MQ Contribution Policy at <http://www.zeromq.org/docs:contributing>.
