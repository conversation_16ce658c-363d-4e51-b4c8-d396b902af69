zmq_inproc(7)
=============


NAME
----
zmq_inproc - 0MQ local in-process (inter-thread) communication transport


SYNOPSIS
--------
The in-process transport passes messages via memory directly between threads
sharing a single 0MQ 'context'.

NOTE: No I/O threads are involved in passing messages using the 'inproc'
transport. Therefore, if you are using a 0MQ 'context' for in-process messaging
only you can initialise the 'context' with zero I/O threads. See
linkzmq:zmq_init[3] for details.


ADDRESSING
----------
A 0MQ endpoint is a string consisting of a 'transport'`://` followed by an
'address'. The 'transport' specifies the underlying protocol to use. The
'address' specifies the transport-specific address to connect to.

For the in-process transport, the transport is `inproc`, and the meaning of
the 'address' part is defined below.


Assigning a local address to a socket
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
When assigning a local address to a 'socket' using _zmq_bind()_ with the
'inproc' transport, the 'endpoint' shall be interpreted as an arbitrary string
identifying the 'name' to create. The 'name' must be unique within the 0MQ
'context' associated with the 'socket' and may be up to 256 characters in
length.  No other restrictions are placed on the format of the 'name'.


Connecting a socket
~~~~~~~~~~~~~~~~~~~
When connecting a 'socket' to a peer address using _zmq_connect()_ with the
'inproc' transport, the 'endpoint' shall be interpreted as an arbitrary string
identifying the 'name' to connect to.  Before version 4.0 he 'name' must have
been previously created by assigning it to at least one 'socket' within the
same 0MQ 'context' as the 'socket' being connected.  Since version 4.0 the
order of _zmq_bind()_ and _zmq_connect()_ does not matter just like for the tcp
transport type.


EXAMPLES
--------
.Assigning a local address to a socket
----
//  Assign the in-process name "#1"
rc = zmq_bind(socket, "inproc://#1");
assert (rc == 0);
//  Assign the in-process name "my-endpoint"
rc = zmq_bind(socket, "inproc://my-endpoint");
assert (rc == 0);
----

.Connecting a socket
----
//  Connect to the in-process name "#1"
rc = zmq_connect(socket, "inproc://#1");
assert (rc == 0);
//  Connect to the in-process name "my-endpoint"
rc = zmq_connect(socket, "inproc://my-endpoint");
assert (rc == 0);
----


SEE ALSO
--------
linkzmq:zmq_bind[3]
linkzmq:zmq_connect[3]
linkzmq:zmq_ipc[7]
linkzmq:zmq_tcp[7]
linkzmq:zmq_pgm[7]
linkzmq:zmq_vmci[7]
linkzmq:zmq[7]


AUTHORS
-------
This page was written by the 0MQ community. To make a change please
read the 0MQ Contribution Policy at <http://www.zeromq.org/docs:contributing>.
