zmq_msg_more(3)
===============


NAME
----
zmq_msg_more - indicate if there are more message parts to receive


SYNOPSIS
--------
*int zmq_msg_more (zmq_msg_t '*message');*


DESCRIPTION
-----------
The _zmq_msg_more()_ function indicates whether this is part of a multi-part
message, and there are further parts to receive. This method can safely be
called after _zmq_msg_close()_. This method is identical to _zmq_msg_get()_
with an argument of ZMQ_MORE.


RETURN VALUE
------------
The _zmq_msg_more()_ function shall return zero if this is the final part of
a multi-part message, or the only part of a single-part message. It shall
return 1 if there are further parts to receive.


EXAMPLE
-------
.Receiving a multi-part message
----
zmq_msg_t part;
while (true) {
    //  Create an empty 0MQ message to hold the message part
    int rc = zmq_msg_init (&part);
    assert (rc == 0);
    //  Block until a message is available to be received from socket
    rc = zmq_msg_recv (socket, &part, 0);
    assert (rc != -1);
    if (zmq_msg_more (&part))
        fprintf (stderr, "more\n");
    else {
        fprintf (stderr, "end\n");
        break;
    }
    zmq_msg_close (&part);
}
----


SEE ALSO
--------
linkzmq:zmq_msg_get[3]
linkzmq:zmq_msg_set[3]
linkzmq:zmq_msg_init[3]
linkzmq:zmq_msg_close[3]
linkzmq:zmq[7]


AUTHORS
-------
This page was written by the 0MQ community. To make a change please
read the 0MQ Contribution Policy at <http://www.zeromq.org/docs:contributing>.
