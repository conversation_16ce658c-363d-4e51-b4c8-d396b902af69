zmq(7)
======


NAME
----
zmq - 0MQ lightweight messaging kernel


SYNOPSIS
--------
*#include <zmq.h>*

*cc* ['flags'] 'files' *-lzmq* ['libraries']


DESCRIPTION
-----------
The 0MQ lightweight messaging kernel is a library which extends the standard
socket interfaces with features traditionally provided by specialised
_messaging middleware_ products. 0MQ sockets provide an abstraction of
asynchronous _message queues_, multiple _messaging patterns_, message
filtering (_subscriptions_), seamless access to multiple _transport protocols_
and more.

This documentation presents an overview of 0MQ concepts, describes how 0MQ
abstracts standard sockets and provides a reference manual for the functions
provided by the 0MQ library.


Context
~~~~~~~
The 0MQ 'context' keeps the list of sockets and manages the async I/O thread
and internal queries.

Before using any 0MQ library functions you must create a 0MQ 'context'. When
you exit your application you must destroy the 'context'. These functions let
you work with 'contexts':

Create a new 0MQ context::
    linkzmq:zmq_ctx_new[3]

Work with context properties::
    linkzmq:zmq_ctx_set[3]
    linkzmq:zmq_ctx_get[3]

Destroy a 0MQ context::
    linkzmq:zmq_ctx_shutdown[3]
    linkzmq:zmq_ctx_term[3]

Thread safety
^^^^^^^^^^^^^
A 0MQ 'context' is thread safe and may be shared among as many application
threads as necessary, without any additional locking required on the part of
the caller.

Individual 0MQ 'sockets' are _not_ thread safe except in the case where full
memory barriers are issued when migrating a socket from one thread to another.
In practice this means applications can create a socket in one thread with
_zmq_socket()_ and then pass it to a _newly created_ thread as part of thread
initialisation, for example via a structure passed as an argument to
_pthread_create()_.


Multiple contexts
^^^^^^^^^^^^^^^^^
Multiple 'contexts' may coexist within a single application. Thus, an
application can use 0MQ directly and at the same time make use of any number of
additional libraries or components which themselves make use of 0MQ as long as
the above guidelines regarding thread safety are adhered to.


Messages
~~~~~~~~
A 0MQ message is a discrete unit of data passed between applications or
components of the same application. 0MQ messages have no internal structure and
from the point of view of 0MQ itself they are considered to be opaque binary
data.

The following functions are provided to work with messages:

Initialise a message::
    linkzmq:zmq_msg_init[3]
    linkzmq:zmq_msg_init_size[3]
    linkzmq:zmq_msg_init_data[3]

Sending and receiving a message::
    linkzmq:zmq_msg_send[3]
    linkzmq:zmq_msg_recv[3]

Release a message::
    linkzmq:zmq_msg_close[3]

Access message content::
    linkzmq:zmq_msg_data[3]
    linkzmq:zmq_msg_size[3]
    linkzmq:zmq_msg_more[3]

Work with message properties::
    linkzmq:zmq_msg_gets[3]
    linkzmq:zmq_msg_get[3]
    linkzmq:zmq_msg_set[3]

Message manipulation::
    linkzmq:zmq_msg_copy[3]
    linkzmq:zmq_msg_move[3]


Sockets
~~~~~~~
0MQ sockets present an abstraction of an asynchronous _message queue_, with the
exact queueing semantics depending on the socket type in use. See
linkzmq:zmq_socket[3] for the socket types provided.

The following functions are provided to work with sockets:

Creating a socket::
    linkzmq:zmq_socket[3]

Closing a socket::
    linkzmq:zmq_close[3]

Manipulating socket options::
    linkzmq:zmq_getsockopt[3]
    linkzmq:zmq_setsockopt[3]

Establishing a message flow::
    linkzmq:zmq_bind[3]
    linkzmq:zmq_connect[3]

Sending and receiving messages::
    linkzmq:zmq_msg_send[3]
    linkzmq:zmq_msg_recv[3]
    linkzmq:zmq_send[3]
    linkzmq:zmq_recv[3]
    linkzmq:zmq_send_const[3]

Monitoring socket events::
    linkzmq:zmq_socket_monitor[3]

.Input/output multiplexing
0MQ provides a mechanism for applications to multiplex input/output events over
a set containing both 0MQ sockets and standard sockets. This mechanism mirrors
the standard _poll()_ system call, and is described in detail in
linkzmq:zmq_poll[3]. This API is deprecated, however.

There is a new DRAFT API with multiple zmq_poller_* function, which is described
in linkzmq:zmq_poller[3].


Transports
~~~~~~~~~~
A 0MQ socket can use multiple different underlying transport mechanisms.
Each transport mechanism is suited to a particular purpose and has its own
advantages and drawbacks.

The following transport mechanisms are provided:

Unicast transport using TCP::
    linkzmq:zmq_tcp[7]

Reliable multicast transport using PGM::
    linkzmq:zmq_pgm[7]

Local inter-process communication transport::
    linkzmq:zmq_ipc[7]

Local in-process (inter-thread) communication transport::
    linkzmq:zmq_inproc[7]

Virtual Machine Communications Interface (VMC) transport::
    linkzmq:zmq_vmci[7]

Unreliable unicast and multicast using UDP::
    linkzmq:zmq_udp[7]


Proxies
~~~~~~~
0MQ provides 'proxies' to create fanout and fan-in topologies. A proxy connects
a 'frontend' socket to a 'backend' socket and switches all messages between the
two sockets, opaquely. A proxy may optionally capture all traffic to a third
socket. To start a proxy in an application thread, use linkzmq:zmq_proxy[3].


Security
~~~~~~~~
A 0MQ socket can select a security mechanism. Both peers must use the same
security mechanism.

The following security mechanisms are provided for IPC and TCP connections:

Null security::
    linkzmq:zmq_null[7]

Plain-text authentication using username and password::
    linkzmq:zmq_plain[7]

Elliptic curve authentication and encryption::
    linkzmq:zmq_curve[7]

Generate a CURVE keypair in armored text format::
    linkzmq:zmq_curve_keypair[3]

Derive a CURVE public key from a secret key:
    linkzmq:zmq_curve_public[3]

Converting keys to/from armoured text strings::
    linkzmq:zmq_z85_decode[3]
    linkzmq:zmq_z85_encode[3]


ERROR HANDLING
--------------
The 0MQ library functions handle errors using the standard conventions found on
POSIX systems. Generally, this means that upon failure a 0MQ library function
shall return either a NULL value (if returning a pointer) or a negative value
(if returning an integer), and the actual error code shall be stored in the
'errno' variable.

On non-POSIX systems some users may experience issues with retrieving the
correct value of the 'errno' variable. The _zmq_errno()_ function is provided
to assist in these cases; for details refer to linkzmq:zmq_errno[3].

The _zmq_strerror()_ function is provided to translate 0MQ-specific error codes
into error message strings; for details refer to linkzmq:zmq_strerror[3].


UTILITY
-------
The following utility functions are provided:

Working with atomic counters::
    linkzmq:zmq_atomic_counter_new[3]
    linkzmq:zmq_atomic_counter_set[3]
    linkzmq:zmq_atomic_counter_inc[3]
    linkzmq:zmq_atomic_counter_dec[3]
    linkzmq:zmq_atomic_counter_value[3]
    linkzmq:zmq_atomic_counter_destroy[3]


MISCELLANEOUS
-------------
The following miscellaneous functions are provided:

Report 0MQ library version::
    linkzmq:zmq_version[3]


LANGUAGE BINDINGS
-----------------
The 0MQ library provides interfaces suitable for calling from programs in any
language; this documentation documents those interfaces as they would be used
by C programmers. The intent is that programmers using 0MQ from other languages
shall refer to this documentation alongside any documentation provided by the
vendor of their language binding.

Language bindings ($$C++$$, Python, PHP, Ruby, Java and more) are provided by
members of the 0MQ community and pointers can be found on the 0MQ website.


AUTHORS
-------
This page was written by the 0MQ community. To make a change please
read the 0MQ Contribution Policy at <http://www.zeromq.org/docs:contributing>.


RESOURCES
---------
Main web site: <http://www.zeromq.org/>

Report bugs to the 0MQ development mailing list: <<EMAIL>>


COPYING
-------
Free use of this software is granted under the terms of the GNU Lesser General
Public License (LGPL). For details see the files `COPYING` and `COPYING.LESSER`
included with the 0MQ distribution.
