zmq_msg_init_data(3)
====================


NAME
----
zmq_msg_init_data - initialise 0MQ message from a supplied buffer


SYNOPSIS
--------
*typedef void (zmq_free_fn) (void '*data', void '*hint');*

*int zmq_msg_init_data (zmq_msg_t '*msg', void '*data', size_t 'size', zmq_free_fn '*ffn', void '*hint');*


DESCRIPTION
-----------
The _zmq_msg_init_data()_ function shall initialise the message object
referenced by 'msg' to represent the content referenced by the buffer located
at address 'data', 'size' bytes long. No copy of 'data' shall be performed and
0MQ shall take ownership of the supplied buffer.

If provided, the deallocation function 'ffn' shall be called once the data
buffer is no longer required by 0MQ, with the 'data' and 'hint' arguments
supplied to _zmq_msg_init_data()_.

CAUTION: Never access 'zmq_msg_t' members directly, instead always use the
_zmq_msg_ family of functions.

CAUTION: The deallocation function 'ffn' needs to be thread-safe, since it
will be called from an arbitrary thread.

CAUTION: If the deallocation function is not provided, the allocated memory
will not be freed, and this may cause a memory leak.


CAUTION: The functions _zmq_msg_init()_, _zmq_msg_init_data()_ and
_zmq_msg_init_size()_ are mutually exclusive. Never initialise the same
'zmq_msg_t' twice.


RETURN VALUE
------------
The _zmq_msg_init_data()_ function shall return zero if successful. Otherwise
it shall return `-1` and set 'errno' to one of the values defined below.


ERRORS
------
*ENOMEM*::
Insufficient storage space is available.



EXAMPLE
-------
.Initialising a message from a supplied buffer
----
void my_free (void *data, void *hint) 
{
    free (data);
}

    /*  ...  */

void *data = malloc (6);
assert (data);
memcpy (data, "ABCDEF", 6);
zmq_msg_t msg;
rc = zmq_msg_init_data (&msg, data, 6, my_free, NULL);
assert (rc == 0);
----


SEE ALSO
--------
linkzmq:zmq_msg_init_size[3]
linkzmq:zmq_msg_init[3]
linkzmq:zmq_msg_close[3]
linkzmq:zmq_msg_data[3]
linkzmq:zmq_msg_size[3]
linkzmq:zmq[7]


AUTHORS
-------
This page was written by the 0MQ community. To make a change please
read the 0MQ Contribution Policy at <http://www.zeromq.org/docs:contributing>.
