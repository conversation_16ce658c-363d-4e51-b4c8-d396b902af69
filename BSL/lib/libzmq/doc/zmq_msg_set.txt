zmq_msg_set(3)
==============


NAME
----

zmq_msg_set - set message property


SYNOPSIS
--------
*int zmq_msg_set (zmq_msg_t '*message', int 'property', int 'value');*


DESCRIPTION
-----------
The _zmq_msg_set()_ function shall set the property specified by the
'property' argument to the value of the 'value' argument for the 0MQ
message fragment pointed to by the 'message' argument.

Currently the _zmq_msg_set()_ function does not support any property names.


RETURN VALUE
------------
The _zmq_msg_set()_ function shall return zero if successful. Otherwise it
shall return `-1` and set 'errno' to one of the values defined below.


ERRORS
------
*EINVAL*::
The requested property _property_ is unknown.


SEE ALSO
--------
linkzmq:zmq_msg_get[3]
linkzmq:zmq[7]


AUTHORS
-------
This page was written by the 0MQ community. To make a change please
read the 0MQ Contribution Policy at <http://www.zeromq.org/docs:contributing>.
