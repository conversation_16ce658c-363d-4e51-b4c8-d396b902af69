zmq_atomic_counter_set(3)
=========================


NAME
----
zmq_atomic_counter_set - set atomic counter to new value


SYNOPSIS
--------
*void zmq_atomic_counter_set (void *counter, int value);*


DESCRIPTION
-----------
The _zmq_atomic_counter_set_ function sets the counter to a new value,
in a threadsafe fashion. The largest value that is guaranteed to work
across all platforms is 2^31-1. This function uses platform specific
atomic operations.


RETURN VALUE
------------
The _zmq_atomic_counter_set()_ function has no return value.


EXAMPLE
-------
.Test code for atomic counters
----
void *counter = zmq_atomic_counter_new ();
assert (zmq_atomic_counter_value (counter) == 0);
assert (zmq_atomic_counter_inc (counter) == 0);
assert (zmq_atomic_counter_inc (counter) == 1);
assert (zmq_atomic_counter_inc (counter) == 2);
assert (zmq_atomic_counter_value (counter) == 3);
assert (zmq_atomic_counter_dec (counter) == 1);
assert (zmq_atomic_counter_dec (counter) == 1);
assert (zmq_atomic_counter_dec (counter) == 0);
zmq_atomic_counter_set (counter, 2);
assert (zmq_atomic_counter_dec (counter) == 1);
assert (zmq_atomic_counter_dec (counter) == 0);
zmq_atomic_counter_destroy (&counter);
return 0;
----


SEE ALSO
--------
linkzmq:zmq_atomic_counter_new[3]
linkzmq:zmq_atomic_counter_inc[3]
linkzmq:zmq_atomic_counter_dec[3]
linkzmq:zmq_atomic_counter_value[3]
linkzmq:zmq_atomic_counter_destroy[3]


AUTHORS
-------
This page was written by the 0MQ community. To make a change please
read the 0MQ Contribution Policy at <http://www.zeromq.org/docs:contributing>.
