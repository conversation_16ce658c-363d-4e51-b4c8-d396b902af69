zmq_msg_init(3)
===============


NAME
----
zmq_msg_init - initialise empty 0MQ message


SYNOPSIS
--------
*int zmq_msg_init (zmq_msg_t '*msg');*


DESCRIPTION
-----------
The _zmq_msg_init()_ function shall initialise the message object referenced by
'msg' to represent an empty message.  This function is most useful when called
before receiving a message with _zmq_msg_recv()_.

CAUTION: Never access 'zmq_msg_t' members directly, instead always use the
_zmq_msg_ family of functions.

CAUTION: The functions _zmq_msg_init()_, _zmq_msg_init_data()_ and
_zmq_msg_init_size()_ are mutually exclusive. Never initialise the same
'zmq_msg_t' twice.


RETURN VALUE
------------
The _zmq_msg_init()_ function always returns zero.


ERRORS
------
No errors are defined.


EXAMPLE
-------
.Receiving a message from a socket
----
zmq_msg_t msg;
rc = zmq_msg_init (&msg);
assert (rc == 0);
int nbytes = zmq_msg_recv (socket, &msg, 0);
assert (nbytes != -1);
----


SEE ALSO
--------
linkzmq:zmq_msg_init_size[3]
linkzmq:zmq_msg_init_data[3]
linkzmq:zmq_msg_close[3]
linkzmq:zmq_msg_data[3]
linkzmq:zmq_msg_size[3]
linkzmq:zmq[7]


AUTHORS
-------
This page was written by the 0MQ community. To make a change please
read the 0MQ Contribution Policy at <http://www.zeromq.org/docs:contributing>.
