zmq_strerror(3)
===============


NAME
----
zmq_strerror - get 0MQ error message string


SYNOPSIS
--------
*const char *zmq_strerror (int 'errnum');*


DESCRIPTION
-----------
The _zmq_strerror()_ function shall return a pointer to an error message string
corresponding to the error number specified by the 'errnum' argument. As 0MQ
defines additional error numbers over and above those defined by the operating
system, applications should use _zmq_strerror()_ in preference to the standard
_strerror()_ function.


RETURN VALUE
------------
The _zmq_strerror()_ function shall return a pointer to an error message
string.


ERRORS
------
No errors are defined.


EXAMPLE
-------
.Displaying an error message when a 0MQ context cannot be initialised
----
void *ctx = zmq_init (1, 1, 0);
if (!ctx) {
    printf ("Error occurred during zmq_init(): %s\n", zmq_strerror (errno));
    abort ();
}
----


SEE ALSO
--------
linkzmq:zmq[7]


AUTHORS
-------
This page was written by the 0MQ community. To make a change please
read the 0MQ Contribution Policy at <http://www.zeromq.org/docs:contributing>.

