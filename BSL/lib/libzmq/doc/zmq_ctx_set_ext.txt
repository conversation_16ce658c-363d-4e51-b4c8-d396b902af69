zmq_ctx_set_ext(3)
==================


NAME
----

zmq_ctx_set_ext - set extended context options


SYNOPSIS
--------
*int zmq_ctx_set_ext (void '*context', int 'option_name', const void '*option_value', size_t 'option_len');*


DESCRIPTION
-----------

The _zmq_ctx_set_ext()_ function shall set the option specified by the
'option_name' argument to the value pointed to by the 'option_value' argument
for the 0MQ context pointed to by the 'context' argument. The 'option_len'
argument is the size of the option value in bytes. For options taking a value of
type "character string", the provided byte data should either contain no zero
bytes, or end in a single zero byte (terminating ASCII NUL character).

The _zmq_ctx_set_ext()_ function accepts all the option names accepted by
_zmq_ctx_set()_.
Options that make most sense to set using _zmq_ctx_set_ext()_ instead of
_zmq_ctx_set()_ are the following options:

ZMQ_THREAD_NAME_PREFIX: Set name prefix for I/O threads
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
The 'ZMQ_THREAD_NAME_PREFIX' argument sets a string prefix to each thread
created for the internal context's thread pool. This option is only supported on Linux.
This option is useful to help  debugging done via "top -H" or "gdb"; in case
multiple processes on the system are using ZeroMQ it is useful to provide through
this context option an application-specific prefix to distinguish ZeroMQ background
threads that belong to different processes.
This option only applies before creating any sockets on the context.

[horizontal]
Option value type:: character string
Option value unit:: N/A
Default value:: empty string


RETURN VALUE
------------
The _zmq_ctx_set_ext()_ function returns zero if successful. Otherwise it
returns `-1` and sets 'errno' to one of the values defined below.


ERRORS
------
*EINVAL*::
The requested option _option_name_ is unknown.


EXAMPLE
-------
.Setting a prefix on internal ZMQ threda names:
----
void *context = zmq_ctx_new ();
const char prefix[] = "MyApp";
size_t prefixLen = sizeof(prefix);
zmq_ctx_set (context, ZMQ_THREAD_NAME_PREFIX, &prefix, &prefixLen);

char buff[256];
size_t buffLen = sizeof(buff);
int rc = zmq_ctx_get (context, ZMQ_THREAD_NAME_PREFIX, &buff, &buffLen);
assert (rc == 0);
assert (buffLen == prefixLen);

----


SEE ALSO
--------
linkzmq:zmq_ctx_set[3]
linkzmq:zmq[7]


AUTHORS
-------
This page was written by the 0MQ community. To make a change please
read the 0MQ Contribution Policy at <http://www.zeromq.org/docs:contributing>.
