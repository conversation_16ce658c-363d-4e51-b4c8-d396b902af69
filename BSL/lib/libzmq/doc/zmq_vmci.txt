zmq_vmci(7)
===========


NAME
----
zmq_vmci - 0MQ transport over virtual machine communicatios interface (VMCI) sockets


SYNOPSIS
--------
The VMCI transport passes messages between VMware virtual machines running on the same host,
between virtual machine and the host and within virtual machines (inter-process transport like ipc).

NOTE: Communication between a virtual machine and the host is not supported on Mac OS X 10.9 and above.


ADDRESSING
----------
A 0MQ endpoint is a string consisting of a 'transport'`://` followed by an
'address'. The 'transport' specifies the underlying protocol to use. The
'address' specifies the transport-specific address to connect to.

For the VMCI transport, the transport is `vmci`, and the meaning of
the 'address' part is defined below.


Binding a socket
~~~~~~~~~~~~~~~~
When binding a 'socket' to a local address using _zmq_bind()_ with the 'vmci'
transport, the 'endpoint' shall be interpreted as an 'interface' followed by a
colon and the TCP port number to use.

An 'interface' may be specified by either of the following:

* The wild-card `*`, meaning all available interfaces.
* An integer returned by `VMCISock_GetLocalCID` or `@` (ZeroMQ will call VMCISock_GetLocalCID internally).

The port may be specified by:

* A numeric value, usually above 1024 on POSIX systems.
* The wild-card `*`, meaning a system-assigned ephemeral port.

Unbinding wild-card address from a socket
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
When wild-card `*` 'endpoint' was used in _zmq_bind()_, the caller should use
real 'endpoint' obtained from the ZMQ_LAST_ENDPOINT socket option to unbind
this 'endpoint' from a socket using _zmq_unbind()_.

Connecting a socket
~~~~~~~~~~~~~~~~~~~
When connecting a socket to a peer address using _zmq_connect()_ with the 'vmci'
transport, the 'endpoint' shall be interpreted as a 'peer address' followed by
a colon and the port number to use.

A 'peer address' must be a CID of the peer.


EXAMPLES
--------
.Assigning a local address to a socket
----
//  VMCI port 5555 on all available interfaces
rc = zmq_bind(socket, "vmci://*:5555");
assert (rc == 0);
//  VMCI port 5555 on the local loop-back interface on all platforms
cid = VMCISock_GetLocalCID();
sprintf(endpoint, "vmci://%d:5555", cid);
rc = zmq_bind(socket, endpoint);
assert (rc == 0);
----

.Connecting a socket
----
//  Connecting using a CID
sprintf(endpoint, "vmci://%d:5555", cid);
rc = zmq_connect(socket, endpoint);
assert (rc == 0);
----


SEE ALSO
--------
linkzmq:zmq_bind[3]
linkzmq:zmq_connect[3]
linkzmq:zmq_inproc[7]
linkzmq:zmq_tcp[7]
linkzmq:zmq_pgm[7]
linkzmq:zmq_vmci[7]
linkzmq:zmq_getsockopt[3]
linkzmq:zmq[7]


AUTHORS
-------
This page was written by the 0MQ community. To make a change please
read the 0MQ Contribution Policy at <http://www.zeromq.org/docs:contributing>.
