zmq_recv(3)
===========


NAME
----
zmq_recv - receive a message part from a socket


SYNOPSIS
--------
*int zmq_recv (void '*socket', void '*buf', size_t 'len', int 'flags');*


DESCRIPTION
-----------
The _zmq_recv()_ function shall receive a message from the socket referenced
by the 'socket' argument and store it in the buffer referenced by the 'buf'
argument. Any bytes exceeding the length specified by the 'len' argument shall
be truncated. If there are no messages available on the specified 'socket'
the _zmq_recv()_ function shall block until the request can be satisfied.
The 'flags' argument is a combination of the flags defined below: The 'buf'
argument may be null if len is zero.

*ZMQ_DONTWAIT*::
Specifies that the operation should be performed in non-blocking mode. If there
are no messages available on the specified 'socket', the _zmq_recv()_
function shall fail with 'errno' set to EAGAIN.


Multi-part messages
~~~~~~~~~~~~~~~~~~~
A 0MQ message is composed of 1 or more message parts. 0MQ ensures atomic
delivery of messages: peers shall receive either all _message parts_ of a
message or none at all. The total number of message parts is unlimited except
by available memory.

An application that processes multi-part messages must use the _ZMQ_RCVMORE_
linkzmq:zmq_getsockopt[3] option after calling _zmq_recv()_ to determine if
there are further parts to receive.

RETURN VALUE
------------
The _zmq_recv()_ function shall return number of bytes in the message
if successful. Note that the value can exceed the value of the 'len' parameter
in case the message was truncated. If not successful the function shall return
`-1` and set 'errno' to one of the values defined below.


ERRORS
------
*EAGAIN*::
Non-blocking mode was requested and no messages are available at the moment.
*ENOTSUP*::
The _zmq_recv()_ operation is not supported by this socket type.
*EFSM*::
The _zmq_recv()_ operation cannot be performed on this socket at the moment
due to the socket not being in the appropriate state.  This error may occur with
socket types that switch between several states, such as ZMQ_REP.  See the
_messaging patterns_ section of linkzmq:zmq_socket[3] for more information.
*ETERM*::
The 0MQ 'context' associated with the specified 'socket' was terminated.
*ENOTSOCK*::
The provided 'socket' was invalid.
*EINTR*::
The operation was interrupted by delivery of a signal before a message was
available.


EXAMPLE
-------
.Receiving a message from a socket
----
char buf [256];
nbytes = zmq_recv (socket, buf, 256, 0);
assert (nbytes != -1);
----


SEE ALSO
--------
linkzmq:zmq_send[3]
linkzmq:zmq_getsockopt[3]
linkzmq:zmq_socket[7]
linkzmq:zmq[7]


AUTHORS
-------
This page was written by the 0MQ community. To make a change please
read the 0MQ Contribution Policy at <http://www.zeromq.org/docs:contributing>.
