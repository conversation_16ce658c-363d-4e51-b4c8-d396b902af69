zmq_ctx_new(3)
==============


NAME
----
zmq_ctx_new - create new 0MQ context


SYNOPSIS
--------
*void *zmq_ctx_new ();*


DESCRIPTION
-----------
The _zmq_ctx_new()_ function creates a new 0MQ 'context'.

This function replaces the deprecated function linkzmq:zmq_init[3].

.Thread safety
A 0MQ 'context' is thread safe and may be shared among as many application
threads as necessary, without any additional locking required on the part of
the caller.


RETURN VALUE
------------
The _zmq_ctx_new()_ function shall return an opaque handle to the newly created
'context' if successful. Otherwise it shall return NULL and set 'errno' to one
of the values defined below.


ERRORS
------
No error values are defined for this function.


SEE ALSO
--------
linkzmq:zmq[7]
linkzmq:zmq_ctx_set[3]
linkzmq:zmq_ctx_get[3]
linkzmq:zmq_ctx_term[3]


AUTHORS
-------
This page was written by the 0MQ community. To make a change please
read the 0MQ Contribution Policy at <http://www.zeromq.org/docs:contributing>.
