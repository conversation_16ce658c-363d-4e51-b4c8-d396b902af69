zmq_null(7)
===========


NAME
----
zmq_null - no security or confidentiality


SYNOPSIS
--------
The NULL mechanism is defined by the ZMTP 3.0 specification: 
<http://rfc.zeromq.org/spec:23>. This is the default security mechanism
for ZeroMQ sockets.


SEE ALSO
--------
linkzmq:zmq_plain[7]
linkzmq:zmq_curve[7]
linkzmq:zmq[7]


AUTHORS
-------
This page was written by the 0MQ community. To make a change please
read the 0MQ Contribution Policy at <http://www.zeromq.org/docs:contributing>.
