zmq_msg_recv(3)
===============


NAME
----
zmq_msg_recv - receive a message part from a socket


SYNOPSIS
--------
*int zmq_msg_recv (zmq_msg_t '*msg', void '*socket', int 'flags');*


DESCRIPTION
-----------
The _zmq_msg_recv()_ function is identical to linkzmq:zmq_recvmsg[3], which
shall be deprecated in future versions. _zmq_msg_recv()_ is more consistent
with other message manipulation functions.

The _zmq_msg_recv()_ function shall receive a message part from the socket
referenced by the 'socket' argument and store it in the message referenced by
the 'msg' argument. Any content previously stored in 'msg' shall be properly
deallocated. If there are no message parts available on the specified 'socket'
the _zmq_msg_recv()_ function shall block until the request can be satisfied.
The 'flags' argument is a combination of the flags defined below:

*ZMQ_DONTWAIT*::
Specifies that the operation should be performed in non-blocking mode. If there
are no messages available on the specified 'socket', the _zmq_msg_recv()_
function shall fail with 'errno' set to EAGAIN.


Multi-part messages
~~~~~~~~~~~~~~~~~~~
A 0MQ message is composed of 1 or more message parts. Each message
part is an independent 'zmq_msg_t' in its own right. 0MQ ensures atomic
delivery of messages: peers shall receive either all _message parts_ of a
message or none at all. The total number of message parts is unlimited except
by available memory.

An application that processes multi-part messages must use the _ZMQ_RCVMORE_
linkzmq:zmq_getsockopt[3] option after calling _zmq_msg_recv()_ to determine if
there are further parts to receive.


RETURN VALUE
------------
The _zmq_msg_recv()_ function shall return number of bytes in the message
if successful. Otherwise it shall return `-1` and set 'errno' to one of the
values defined below.


ERRORS
------
*EAGAIN*::
Non-blocking mode was requested and no messages are available at the moment.
*ENOTSUP*::
The _zmq_msg_recv()_ operation is not supported by this socket type.
*EFSM*::
The _zmq_msg_recv()_ operation cannot be performed on this socket at the moment
due to the socket not being in the appropriate state.  This error may occur with
socket types that switch between several states, such as ZMQ_REP.  See the
_messaging patterns_ section of linkzmq:zmq_socket[3] for more information.
*ETERM*::
The 0MQ 'context' associated with the specified 'socket' was terminated.
*ENOTSOCK*::
The provided 'socket' was invalid.
*EINTR*::
The operation was interrupted by delivery of a signal before a message was
available.
*EFAULT*::
The message passed to the function was invalid.


EXAMPLE
-------
.Receiving a message from a socket
----
/* Create an empty 0MQ message */
zmq_msg_t msg;
int rc = zmq_msg_init (&msg);
assert (rc == 0);
/* Block until a message is available to be received from socket */
rc = zmq_msg_recv (&msg, socket, 0);
assert (rc != -1);
/* Release message */
zmq_msg_close (&msg);
----

.Receiving a multi-part message
----
int more;
size_t more_size = sizeof (more);
do {
    /* Create an empty 0MQ message to hold the message part */
    zmq_msg_t part;
    int rc = zmq_msg_init (&part);
    assert (rc == 0);
    /* Block until a message is available to be received from socket */
    rc = zmq_msg_recv (&part, socket, 0);
    assert (rc != -1);
    /* Determine if more message parts are to follow */
    rc = zmq_getsockopt (socket, ZMQ_RCVMORE, &more, &more_size);
    assert (rc == 0);
    zmq_msg_close (&part);
} while (more);
----


SEE ALSO
--------
linkzmq:zmq_recv[3]
linkzmq:zmq_send[3]
linkzmq:zmq_msg_send[3]
linkzmq:zmq_getsockopt[3]
linkzmq:zmq_socket[7]
linkzmq:zmq[7]


AUTHORS
-------
This page was written by the 0MQ community. To make a change please
read the 0MQ Contribution Policy at <http://www.zeromq.org/docs:contributing>.
