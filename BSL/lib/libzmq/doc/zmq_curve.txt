zmq_curve(7)
============


NAME
----
zmq_curve - secure authentication and confidentiality


SYNOPSIS
--------
The CURVE mechanism defines a mechanism for secure authentication and 
confidentiality for communications between a client and a server. CURVE 
is intended for use on public networks. The CURVE mechanism is defined 
by this document: <http://rfc.zeromq.org/spec:25>.


CLIENT AND SERVER ROLES
-----------------------
A socket using CURVE can be either client or server, at any moment, but 
not both. The role is independent of bind/connect direction.

A socket can change roles at any point by setting new options. The role
affects all zmq_connect and zmq_bind calls that follow it.

To become a CURVE server, the application sets the ZMQ_CURVE_SERVER option
on the socket, and then sets the ZMQ_CURVE_SECRETKEY option to provide the
socket with its long-term secret key. The application does not provide the
socket with its long-term public key, which is used only by clients.

To become a CURVE client, the application sets the ZMQ_CURVE_SERVERKEY 
option with the long-term public key of the server it intends to connect
to, or accept connections from, next. The application then sets the 
ZMQ_CURVE_PUBLICKEY and ZMQ_CURVE_SECRETKEY options with its client 
long-term key pair.

If the server does authentication it will be based on the client's long
term public key.

KEY ENCODING
------------
The standard representation for keys in source code is either 32 bytes of
base 256 (binary) data, or 40 characters of base 85 data encoded using the
Z85 algorithm defined by http://rfc.zeromq.org/spec:32.

The Z85 algorithm is designed to produce printable key strings for use in
configuration files, the command line, and code. There is a reference
implementation in C at https://github.com/zeromq/rfc/tree/master/src.

    
TEST KEY VALUES
---------------
For test cases, the client shall use this long-term key pair (specified
as hexadecimal and in Z85):

----
public: 
    BB88471D65E2659B30C55A5321CEBB5AAB2B70A398645C26DCA2B2FCB43FC518
    Yne@$w-vo<fVvi]a<NY6T1ed:M$fCG*[IaLV{hID

secret: 
    7BB864B489AFA3671FBE69101F94B38972F24816DFB01B51656B3FEC8DFD0888
    D:)Q[IlAW!ahhC2ac:9*A}h:p?([4%wOTJ%JR%cs
----

And the server shall use this long-term key pair (specified as hexadecimal 
and in Z85):

----
public: 
    54FCBA24E93249969316FB617C872BB0C1D1FF14800427C594CBFACF1BC2D652
    rq:rM>}U?@Lns47E1%kR.o@n%FcmmsL/@{H8]yf7

secret: 
    8E0BDD697628B91D8F245587EE95C5B04D48963F79259877B49CD9063AEAD3B7
    JTKVSB%%)wK0E.X)V>+}o?pNmC{O&4W4b!Ni{Lh6
----

SEE ALSO
--------
linkzmq:zmq_z85_encode[3]
linkzmq:zmq_z85_decode[3]
linkzmq:zmq_setsockopt[3]
linkzmq:zmq_null[7]
linkzmq:zmq_plain[7]
linkzmq:zmq[7]


AUTHORS
-------
This page was written by the 0MQ community. To make a change please
read the 0MQ Contribution Policy at <http://www.zeromq.org/docs:contributing>.
