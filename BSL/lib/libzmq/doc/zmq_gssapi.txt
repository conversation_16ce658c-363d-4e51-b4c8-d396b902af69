zmq_gssapi(7)
============


NAME
----
zmq_gssapi - secure authentication and confidentiality


SYNOPSIS
--------

The GSSAPI mechanism defines a mechanism for secure authentication and
confidentiality for communications between a client and a server using the
Generic Security Service Application Program Interface (GSSAPI).  The GSSAPI
mechanism can be used on both public and private networks.  GSSAPI itself is
defined in IETF RFC-2743: <http://tools.ietf.org/html/rfc2743>. The ZeroMQ
GSSAPI mechanism is defined by this document: <http://rfc.zeromq.org/spec:38>.


CLIENT AND SERVER ROLES
-----------------------
A socket using GSSAPI can be either client or server, but not both.

To become a GSSAPI server, the application sets the ZMQ_GSSAPI_SERVER
option on the socket.

To become a GSSAPI client, the application sets the ZMQ_GSSAPI_SERVICE_PRINCIPAL
option to the name of the principal on the server to which it intends to
connect.

On client or server, the application may additionally set the
ZMQ_GSSAPI_PRINCIPAL option to provide the socket with the name of the
principal for whom GSSAPI credentials should be acquired.  If this option
is not set, default credentials are used.


OPTIONAL ENCRYPTION
-------------------
By default, the GSSAPI mechanism will encrypt all communications between client
and server.  If encryption is not desired (e.g. on private networks), the
client and server applications can disable it by setting the
ZMQ_GSSAPI_PLAINTEXT option.  Both the client and server must set this option
to the same value.


PRINCIPAL NAMES
---------------
Principal names specified with the ZMQ_GSSAPI_SERVICE_PRINCIPAL or
ZMQ_GSSAPI_PRINCIPAL options are interpreted as "host based" name types
by default.  The ZMQ_GSSAPI_PRINCIPAL_NAMETYPE and
ZMQ_GSSAPI_SERVICE_PRINCIPAL_NAMETYPE options may be used to change the
name type to one of:

*ZMQ_GSSAPI_NT_HOSTBASED*::
The name should be of the form "service" or "service@hostname",
which will parse into a principal of "service/hostname"
in the local realm.  This is the default name type.
*ZMQ_GSSAPI_NT_USER_NAME*::
The name should be a local username, which will parse into a single-component
principal in the local realm.
*ZMQ_GSSAPI_NT_KRB5_PRINCIPAL*::
The name is a principal name string.  This name type only works with
the krb5 GSSAPI mechanism.


SEE ALSO
--------
linkzmq:zmq_setsockopt[3]
linkzmq:zmq_null[7]
linkzmq:zmq_curve[7]
linkzmq:zmq[7]


AUTHORS
-------
This page was written by the 0MQ community. To make a change please
read the 0MQ Contribution Policy at <http://www.zeromq.org/docs:contributing>.
