zmq_plain(7)
============


NAME
----
zmq_plain - clear-text authentication


SYNOPSIS
--------
The PLAIN mechanism defines a simple username/password mechanism that 
lets a server authenticate a client. PLAIN makes no attempt at security 
or confidentiality. It is intended for use on internal networks where 
security requirements are low. The PLAIN mechanism is defined by this 
document: <http://rfc.zeromq.org/spec:24>.


USAGE
-----
To use PLAIN, the server shall set the ZMQ_PLAIN_SERVER option, and the 
client shall set the ZMQ_PLAIN_USERNAME and ZMQ_PLAIN_PASSWORD socket 
options. Which peer binds, and which connects, is not relevant.


SEE ALSO
--------
linkzmq:zmq_setsockopt[3]
linkzmq:zmq_null[7]
linkzmq:zmq_curve[7]
linkzmq:zmq[7]


AUTHORS
-------
This page was written by the 0MQ community. To make a change please
read the 0MQ Contribution Policy at <http://www.zeromq.org/docs:contributing>.
