zmq_close(3)
============


NAME
----
zmq_close - close 0MQ socket


SYNOPSIS
--------
*int zmq_close (void '*socket');*


DESCRIPTION
-----------
The _zmq_close()_ function shall destroy the socket referenced by the 'socket'
argument. Any outstanding messages physically received from the network but not
yet received by the application with _zmq_recv()_ shall be discarded. The
behaviour for discarding messages sent by the application with _zmq_send()_ but
not yet physically transferred to the network depends on the value of the
_ZMQ_LINGER_ socket option for the specified 'socket'.

_zmq_close()_ must be called exactly once for each socket. If it is never called,
_zmq_ctx_term()_ will block forever. If it is called multiple times for the same 
socket or if 'socket' does not point to a socket, the behaviour is undefined.

NOTE: The default setting of _ZMQ_LINGER_ does not discard unsent messages;
this behaviour may cause the application to block when calling _zmq_ctx_term()_.
For details refer to linkzmq:zmq_setsockopt[3] and linkzmq:zmq_ctx_term[3].

NOTE: This API will complete asynchronously, so not everything will be deallocated
after it returns. See above for details about linger.


RETURN VALUE
------------
The _zmq_close()_ function shall return zero if successful. Otherwise it shall
return `-1` and set 'errno' to one of the values defined below.


ERRORS
------
*ENOTSOCK*::
The provided 'socket' was NULL.


SEE ALSO
--------
linkzmq:zmq_socket[3]
linkzmq:zmq_ctx_term[3]
linkzmq:zmq_setsockopt[3]
linkzmq:zmq[7]


AUTHORS
-------
This page was written by the 0MQ community. To make a change please
read the 0MQ Contribution Policy at <http://www.zeromq.org/docs:contributing>.
